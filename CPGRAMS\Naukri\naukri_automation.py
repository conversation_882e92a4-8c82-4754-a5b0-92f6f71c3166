from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, StaleElementReferenceException, WebDriverException,NoSuchElementException
import time
import re
import sys
import random
import pandas as pd
import requests

# # Setup WebDriver
# options = webdriver.ChromeOptions()
# options.add_argument("--start-maximized")
# driver = webdriver.Chrome(options=options)
# wait = WebDriverWait(driver, 20)

# try:
#     driver.get("https://www.naukri.com/")
#     login_button = wait.until(EC.element_to_be_clickable((By.ID, "login_Layer")))
#     login_button.click()

#     email_field = wait.until(EC.element_to_be_clickable((By.XPATH, "//input[@type='text']")))
#     email_field.send_keys("<EMAIL>")
#     password_field = driver.find_element(By.XPATH, "//input[@type='password']")
#     password_field.send_keys("badshaah")
#     password_field.send_keys(Keys.RETURN)

#     wait.until(EC.url_contains("naukri.com"))

#     search_bar = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "div.nI-gNb-sb__main")))
#     search_bar.click()

#     search_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "input.suggestor-input")))
#     search_input.send_keys("Data Analyst, Data Science, Machine Learning, Artificial Intelligence")
    
#     time.sleep(3) 
#     experience_input = wait.until(EC.presence_of_element_located((By.ID, "experienceDD")))

#     experience_input.click()
#     experience_options = wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, "ul.dropdown li")))
#     index_to_select = 1
#     experience_options[index_to_select].click()

#     location_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR,  "div.nI-gNb-sb__locations input.suggestor-input")))
#     location_input.click()
#     location_input.clear()
#     location_input.send_keys("Faridabad,Noida,Gurugram,Bangalore,Pune,Hyderabad,Chennai,Mumbai,Delhi")

#     search_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "button.nI-gNb-sb__icon-wrapper")))
#     search_button.click()
#     try:
#         try:
#             salary_filter_3_6 = wait.until(EC.element_to_be_clickable((By.XPATH, "//span[@title='3-6 Lakhs']")))
#             salary_filter_3_6.click()  # Click on the 3-6 Lakhs salary filter
#             print("1. Salary filter '3-6 Lakhs' clicked successfully")
#             time.sleep(3)
#         except Exception as e:
#             print("1. Failed to find the 3-6 Lakhs salary filter")
#             print(f"Error: {e}")
#         try:
#             salary_filter = wait.until(EC.element_to_be_clickable((By.XPATH, "//span[@title='6-10 Lakhs']")))
#             salary_filter.click()  # Click on the first salary filter option
#             print("1. Salary filter '6-10 Lakhs' clicked successfully")
#             time.sleep(3)
#         except Exception as e:
#             print("1. Failed to find the first salary filter option")
#             print(f"Error: {e}")
#         try:
#             salary_filter = wait.until(EC.element_to_be_clickable((By.XPATH, "//span[@title='10-15 Lakhs']")))
#             salary_filter.click()  # Click on the first salary filter option
#             print("1. Salary filter '10-15 Lakhs' clicked successfully")
#             time.sleep(2)
#         except Exception as e:
#             print("1. Failed to find the first salary filter option")
#             print(f"Error: {e}")
#     except Exception as e:
#         print(f"Error: {e}")
#         print("Salary filter not found after trying all conditions.")
#     try:
#         view_more_button = driver.find_element(By.ID, "educationViewMoreId")
#         view_more_button.click()

#         time.sleep(15)
#         WebDriverWait(driver, 7).until(EC.visibility_of_element_located((By.ID, "tooltip")))

#         WebDriverWait(driver, 10).until(EC.presence_of_all_elements_located((By.XPATH, "//label[@class='styles_chkLbl__n2x09']")))
#         apply_button = WebDriverWait(driver, 10).until(
#             EC.element_to_be_clickable((By.CSS_SELECTOR, ".styles_filter-apply-btn__MDAUd"))
#         )
#         apply_button.click()
#         time.sleep(2)
#         print("Education filters applied successfully")
#     except Exception as e:
#         print(f"Error: {e}")
#         print("Failed to apply education filters")

# finally:
#     time.sleep(10)
#     driver.quit()

# Constants
MAX_RETRIES = 3
PAGE_LOAD_TIMEOUT = 30
API_URL = "http://localhost:10000/generate"
# Store job applications data
applications = []

def chat_with_bot(question):
    """Send chatbot question to API and get the response"""
    try:
        response = requests.post(API_URL, json={"message": question}, timeout=120)
        print(f"API response: {response.text}")
        if response.status_code == 200:
            return response.json()["response"]["content"]
    except requests.exceptions.RequestException:
        return None
    
def extract_phone_number(driver):
    """Extract phone numbers from job description using regex"""
    try:
        jd_container = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "section.styles_job-desc-container__txpYf"))
        )
        jd_text = jd_container.text
        
        # Enhanced phone number regex pattern
        phone_regex = r'\b(?:\+?\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}\b|\b\d{10}\b|\b\d{5}[-.\s]?\d{5}\b'
        numbers_found = re.findall(phone_regex, jd_text)
        
        # Clean and validate numbers
        valid_numbers = []
        for num in numbers_found:
            # Remove non-digit characters
            cleaned_num = re.sub(r'\D', '', num)
            # Check for valid length (9-10 digits)
            if 9 <= len(cleaned_num) <= 10:
                # Format with last 10 digits if longer
                valid_numbers.append(cleaned_num[-10:] if len(cleaned_num) > 10 else cleaned_num)
        
        return ", ".join(valid_numbers) if valid_numbers else "Not Found"
        
    except Exception as e:
        print(f"Error extracting phone number: {str(e)}")
        return "Error"

def setup_driver():
    options = webdriver.ChromeOptions()
    options.add_argument("--start-maximized")
    options.add_argument("--disable-notifications")
    driver = webdriver.Chrome(options=options)
    driver.set_page_load_timeout(PAGE_LOAD_TIMEOUT)
    return driver, WebDriverWait(driver, 20)

def handle_authentication(driver, wait):
    try:
        driver.get("https://www.naukri.com/")
        login_button = wait.until(EC.element_to_be_clickable((By.ID, "login_Layer")))
        login_button.click()

        email_field = wait.until(EC.element_to_be_clickable((By.XPATH, "//input[@type='text']")))
        #email_field.send_keys("<EMAIL>")
        email_field.send_keys("<EMAIL>")
        
        password_field = driver.find_element(By.XPATH, "//input[@type='password']")
        #password_field.send_keys("Deepak@2212")
        password_field.send_keys("badshaah")
        password_field.send_keys(Keys.RETURN)

        wait.until(EC.url_contains("naukri.com"))
        print("Authentication successful")
    except Exception as e:
        print(f"Authentication failed: {str(e)}")
        raise

def configure_search(wait):
    try:
        search_inputs = [
            # "Generative AI, Data analytics, Data Analyst, data engineer, data engineering, python developer, Machine Learning, Artificial Intelligence, data science",
            # "Machine Learning, Artificial Intelligence, data science, python, business analyst",
            # "data science, data scientist, data analyst, data science analyst, business analyst, data analytics, machine learning, natural language processing, artificial intelligence",
            "Junior Data scientist",
            # "Data science developer, Data analyst",
            # "Data analyst",
            # "Generative AI",
            # "Python Developer",
            # "IT recruiter",
            # "AI"
        ]
        # Use only sys.argv[1] as the search string if provided
        if len(sys.argv) > 1:
            search_text = sys.argv[1]
        else:
            search_text = random.choice(search_inputs)

        search_bar = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "div.nI-gNb-sb__main")))
        search_bar.click()
        search_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "input.suggestor-input")))
        search_input.clear()
        search_input.send_keys(search_text)
        #Experience filter
        experience_input = wait.until(EC.presence_of_element_located((By.ID, "experienceDD")))
        experience_input.click()
        experience_options = wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, "ul.dropdown li")))
        experience_options[1].click()

        # Location filter
        location_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "div.nI-gNb-sb__locations input.suggestor-input")))
        #location_input.send_keys("Noida,Gurugram,Pune,Delhi,Banglore")
        #location_input.send_keys("Noida")
        location_input.send_keys(" ")

        # Execute search
        wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "button.nI-gNb-sb__icon-wrapper"))).click()
        print("Search configured successfully")
    except Exception as e:
        print(f"Search configuration failed: {str(e)}")
        raise

def apply_filters(wait):
    try:
        salary_ranges = ['3-6 Lakhs', '6-10 Lakhs', '10-15 Lakhs']
        for salary in salary_ranges:
            try:
                wait.until(EC.element_to_be_clickable((By.XPATH, f"//span[@title='{salary}']"))).click()
                print(f"Applied salary filter: {salary}")
                time.sleep(1)
            except:
                print(f"Failed to apply {salary} filter")

        # Education filter
        wait.until(EC.element_to_be_clickable((By.ID, "educationViewMoreId"))).click()
        time.sleep(8)
        wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, ".styles_filter-apply-btn__MDAUd"))).click()
        print("Education filters applied")

        # Department filter: Data Science & Analytics
        try:
            # Expand Department filter if needed
            department_heading = wait.until(EC.element_to_be_clickable((By.XPATH, "//span[text()='Department']")))
            driver = wait._driver
            driver.execute_script("arguments[0].scrollIntoView(true);", department_heading)
            department_heading.click()
            time.sleep(1)
        except Exception:
            pass  # Already expanded
        try:
            driver = wait._driver
            # Try clicking the label first
            data_science_label = wait.until(EC.element_to_be_clickable((
                By.XPATH, "//label[@for='chk-Data Science & Analytics-functionAreaIdGid-']"
            )))
            driver.execute_script("arguments[0].scrollIntoView(true);", data_science_label)
            try:
                driver.execute_script("arguments[0].click();", data_science_label)
                print("Department filter 'Data Science & Analytics' label clicked")
                time.sleep(1)
            except Exception as e:
                print(f"Label click failed: {str(e)}. Trying input checkbox directly.")
                # Try clicking the input checkbox directly if label fails
                data_science_checkbox = wait.until(EC.element_to_be_clickable((
                    By.ID, "chk-Data Science & Analytics-functionAreaIdGid-"
                )))
                driver.execute_script("arguments[0].scrollIntoView(true);", data_science_checkbox)
                driver.execute_script("arguments[0].click();", data_science_checkbox)
                print("Department filter 'Data Science & Analytics' checkbox clicked")
                time.sleep(1)
        except Exception as e:
            print(f"Failed to apply department filter: {str(e)}")
    except Exception as e:
        print(f"Filter application failed: {str(e)}")
        raise
        
    # try:
    #     # Freshness
    #     print("Applying freshness filter")
    #     freshness_button = wait.until(EC.element_to_be_clickable((By.ID, "filter-freshness")))
    #     freshness_button.click()
    #     freshness_options = wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, "ul[data-filter-id='freshness'] li")))
    #     freshness_options[1].click()  # Select 'Last 15 days'
    #     print("Freshness filter applied")
    #     time.sleep(2)  # Wait for filters to update
    # except Exception as e:
    #     print(f"Failed to apply freshness filter: {str(e)}")
    #     time.sleep(1)

    # Try to apply company type filters by clicking on labels using the updated selectors with -expanded suffix
    try:
        # Wait for company type filter section to be visible
        company_filter_button = wait.until(EC.element_to_be_clickable((By.ID, "filter-qbusinessSize")))
        driver = wait._driver  # Get the driver from the WebDriverWait object
        driver.execute_script("arguments[0].click();", company_filter_button)
        print("Clicked on company type filter section")
        time.sleep(2)
        
        # Click on the LABELS for the company types with expanded suffix
        company_labels = [
            "chk-Foreign MNC-qbusinessSize-expanded",
            "chk-Corporate-qbusinessSize-expanded", 
            "chk-Indian MNC-qbusinessSize-expanded"
        ]
        
        for label_id in company_labels:
            try:
                # Find the label by its 'for' attribute, which now has -expanded suffix
                label_element = wait.until(EC.presence_of_element_located((
                    By.XPATH, f"//label[@for='{label_id}']"
                )))
                driver.execute_script("arguments[0].click();", label_element)
                print(f"Selected company type: {label_id}")
                time.sleep(1)
            except Exception as e:
                print(f"Failed to click company type label {label_id}: {str(e)}")
        
        print("Company type filters applied")
        time.sleep(1)
    except Exception as e:
        print(f"Failed to apply company type filters: {str(e)}")
        time.sleep(1)
    
    # Try to apply role category filters by clicking on labels (also with potential expanded suffix)
    try:
        # Wait for role category filter section
        role_filter_button = wait.until(EC.element_to_be_clickable((By.ID, "filter-glbl_qcrc")))
        driver = wait._driver
        driver.execute_script("arguments[0].click();", role_filter_button)
        print("Clicked on role category filter section")
        time.sleep(2)
        
        # Try first with expanded suffix
        role_categories = [
            "Software Development", 
            "Business Intelligence & Analytics", 
            "DBA / Data warehousing"
        ]
        
        for role in role_categories:
            try:
                # First try with -expanded suffix
                expanded_label = f"chk-{role}-glbl_qcrc-expanded"
                label_selector = f"//label[@for='{expanded_label}']"
                
                # Check if the label with expanded suffix exists
                if len(driver.find_elements(By.XPATH, label_selector)) > 0:
                    label_element = wait.until(EC.presence_of_element_located((By.XPATH, label_selector)))
                else:
                    # Fallback to regular id without expanded suffix
                    regular_label = f"chk-{role}-glbl_qcrc-"
                    label_element = wait.until(EC.presence_of_element_located((
                        By.XPATH, f"//label[@for='{regular_label}']"
                    )))
                
                driver.execute_script("arguments[0].click();", label_element)
                print(f"Selected role category: {role}")
                time.sleep(1)
            except Exception as e:
                print(f"Failed to click role category {role}: {str(e)}")
                
        print("Role category filters applied")
        time.sleep(1)
    except Exception as e:
        print(f"Failed to apply role category filters: {str(e)}")
        # Continue even if filter fails

def process_job_page(driver, wait, current_page):
    global applications

    def get_new_window(original_handles):
        """Wait for and return new window handle"""
        try:
            WebDriverWait(driver, 10).until(
                lambda d: len(d.window_handles) > len(original_handles)
            )
            return [h for h in driver.window_handles if h not in original_handles][0]
        except TimeoutException:
            print("New window did not open in time")
            return None

    def handle_chatbot(driver):
        """Handle chatbot interaction with multiple safety checks"""
        status = "Application Failed"
        chatbot_used = "No"
        max_interactions = 10
        interactions = 0

        # First, check if this is a LinkedIn form
        try:
            linkedin_form = driver.find_elements(By.CSS_SELECTOR, "div[data-test-form-element]")
            if linkedin_form:
                print("LinkedIn application form detected")
                if handle_linkedin_form(driver):
                    # Look for submit button
                    try:
                        submit_btn = WebDriverWait(driver, 10).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, "button[aria-label*='Submit'], button[type='submit']"))
                        )
                        submit_btn.click()
                        print("Submitted LinkedIn application")
                        status = "Successfully applied via LinkedIn form"
                        return status, "No"
                    except Exception as e:
                        print(f"Submit button not found: {str(e)}")
        except Exception as e:
            print(f"LinkedIn form check failed: {str(e)}")

        try:
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.CLASS_NAME, "chatbot_MessageContainer"))
            )
            chatbot_used = "Yes"
            print("Chatbot detected - starting conversation")

            while interactions < max_interactions:
                try:
                    # Check for success message first
                    success_elements = driver.find_elements(
                        By.XPATH, '//div[contains(@class, "apply-status-header") and contains(@class, "green")]'
                    )
                    if success_elements and "successfully applied" in success_elements[0].text.lower():
                        status = "Successfully applied via chatbot"
                        break

                    # Get all bot messages
                    bot_messages = driver.find_elements(By.CSS_SELECTOR, 'div.botMsg.msg div span')
                    if not bot_messages:
                        break

                    last_question = bot_messages[-1].text.strip()
                    print(f"Chatbot question ({interactions+1}/{max_interactions}): {last_question}")

                    # Handle radio buttons with enhanced debugging
                    radio_handled = False

                    # First, check if radio buttons are present
                    radio_elements = driver.find_elements(By.CSS_SELECTOR, "input[type='radio']")
                    if radio_elements:
                        print(f"Found {len(radio_elements)} radio buttons")

                        # Print radio button details for debugging
                        for i, radio in enumerate(radio_elements):
                            try:
                                radio_id = radio.get_attribute("id")
                                radio_value = radio.get_attribute("value")
                                is_displayed = radio.is_displayed()
                                is_enabled = radio.is_enabled()
                                print(f"Radio {i+1}: ID='{radio_id}', Value='{radio_value}', Displayed={is_displayed}, Enabled={is_enabled}")
                            except Exception as e:
                                print(f"Error getting radio {i+1} details: {e}")

                        # Try robust radio click
                        try:
                            if robust_click_radio(driver):
                                print("Radio button clicked successfully")

                                # Wait a moment for any UI updates
                                time.sleep(1)

                                # Look for save/submit button after radio selection
                                save_button_selectors = [
                                    "button[type='submit']",
                                    "button.savesrc__button",
                                    "button[class*='save']",
                                    "button[class*='submit']",
                                    "button[class*='next']",
                                    ".chatbot_SendMessageContainer button",
                                    "button.sendMsg"
                                ]

                                for selector in save_button_selectors:
                                    try:
                                        save_button = WebDriverWait(driver, 3).until(
                                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                                        )
                                        driver.execute_script("arguments[0].click();", save_button)
                                        print(f"Clicked save/submit button using selector: {selector}")
                                        radio_handled = True
                                        time.sleep(2)  # Wait for next question
                                        break
                                    except Exception:
                                        continue

                                if not radio_handled:
                                    print("No save button found after radio selection, continuing...")
                                    radio_handled = True  # Consider it handled even without save button
                                    time.sleep(2)
                            else:
                                print("Failed to click any radio button")
                        except Exception as e:
                            print(f"Radio button interaction failed: {e}")
                    else:
                        print("No radio buttons found")

                    if not radio_handled:
                        # Fallback to text input
                        try:
                            input_field = WebDriverWait(driver, 5).until(
                                EC.element_to_be_clickable((By.CSS_SELECTOR, "#userInput__f9ndj8bujInputBox, div[contenteditable='true'].textArea"))
                            )
                            answer = chat_with_bot(last_question)
                            if not answer:
                                raise ValueError("Empty response from chatbot API")

                            input_field.clear()
                            input_field.send_keys(answer)
                            print("Provided text input")

                            # Click send button
                            send_button = WebDriverWait(driver, 5).until(
                                EC.element_to_be_clickable((By.CLASS_NAME, "sendMsg"))
                            )
                            send_button.click()
                            print("Sent chatbot response")
                            
                        except (TimeoutException, NoSuchElementException):
                            print("No recognizable input method found")

                    interactions += 1
                    time.sleep(2)  # Allow chatbot processing

                except Exception as e:
                    print(f"Chatbot interaction error: {str(e)}")
                    break

        except TimeoutException:
            print("No chatbot detected within timeout")
        except Exception as e:
            print(f"Chatbot handling failed: {str(e)}")

        return status, chatbot_used

    try:
        # Initial job list retrieval with retries
        jobs = wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, "div.srp-jobtuple-wrapper")))
        print(f"\nFound {len(jobs)} jobs on page {current_page}")

        for idx in range(1, len(jobs)+1):
            job_processed = False
            for retry in range(MAX_RETRIES + 1):
                try:
                    if retry > 0:
                        print(f"Retry {retry}/{MAX_RETRIES} for job {idx}")
                        jobs = wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, "div.srp-jobtuple-wrapper")))
                        if idx-1 >= len(jobs):
                            print(f"Job index {idx} out of bounds after refresh, skipping")
                            break
                        job = jobs[idx-1]

                    # Refresh job reference each iteration
                    jobs = wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, "div.srp-jobtuple-wrapper")))
                    job = jobs[idx-1] if idx-1 < len(jobs) else None
                    if not job:
                        print(f"Job index {idx} no longer exists, skipping")
                        break

                    link = WebDriverWait(job, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "a.title")))
                    job_url = link.get_attribute('href').strip()
                    if not job_url:
                        raise ValueError("Empty job URL")

                    print(f"\nProcessing job {idx}/{len(jobs)}: {job_url[:60]}...")

                    # Window management
                    original_windows = driver.window_handles
                    driver.execute_script("window.open(arguments[0]);", job_url)
                    new_window = get_new_window(original_windows)
                    
                    if not new_window:
                        raise WebDriverException("Failed to open new window")

                    driver.switch_to.window(new_window)
                    status = "Application Failed"
                    chatbot_used = "No"
                    phone_number = "Not Checked"

                    try:
                        # Check for already applied status
                        try:
                            already_applied = WebDriverWait(driver, 5).until(
                                EC.visibility_of_element_located((By.ID, "already-applied"))
                            )
                            status = "Already Applied"
                            print(f"Already applied to position {idx}")
                            try:
                                phone_number = extract_phone_number(driver)
                                print(f"Found phone numbers: {phone_number}")
                            except Exception as e:
                                print(f"Phone number extraction failed: {str(e)}")
                                phone_number = "Error"
                        except TimeoutException:
                            pass

                        # Check for company site redirect
                        try:
                            company_site_btn = WebDriverWait(driver, 3).until(
                                EC.visibility_of_element_located((By.ID, "company-site-button"))
                            )
                            status = "External Application Required"
                            print(f"Job {idx} requires company site application")
                        except TimeoutException:
                            pass

                        # Handle if either special case found
                        if status in ("Already Applied", "External Application Required"):
                            applications.append({
                                "Job Link": job_url,
                                "Status": status,
                                "Chatbot Used": chatbot_used,
                                "Number": phone_number,  # New field
                                "Page": current_page,
                                "Timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                            })
                            driver.close()
                            driver.switch_to.window(original_windows[0])
                            job_processed = True
                            break  # Exit retry loop

                        # Proceed with normal application flow
                        apply_btn = WebDriverWait(driver, 15).until(
                            EC.element_to_be_clickable((By.ID, "apply-button")))
                        apply_btn.click()
                        print("Clicked apply button")

                        # Handle application process
                        time.sleep(2)  # Initial load time
                        status, chatbot_used = handle_chatbot(driver)

                        # Final success check
                        try:
                            success_msg = WebDriverWait(driver, 10).until(
                                EC.visibility_of_element_located((By.XPATH, 
                                    '//div[contains(@class, "apply-status-header") and contains(@class, "green")]'))
                            )
                            if "successfully applied" in success_msg.text.lower():
                                status = "Successfully applied directly"
                        except TimeoutException:
                            pass

                    except Exception as e:
                        print(f"Application process error: {str(e)}")
                        status = f"Application Error: {str(e)}"

                    # Record application status
                    applications.append({
                        "Job Link": job_url,
                        "Status": status,
                        "Chatbot Used": chatbot_used,
                        "Page": current_page,
                        "Timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                    })

                    # Cleanup windows
                    driver.close()
                    driver.switch_to.window(original_windows[0])
                    job_processed = True
                    break

                except StaleElementReferenceException:
                    print(f"Stale element reference while processing job {idx}")
                    time.sleep(2)
                except Exception as e:
                    print(f"Error processing job {idx}: {str(e)}")
                    if retry == MAX_RETRIES:
                        print(f"Max retries reached for job {idx}")
                    time.sleep(1)

            if not job_processed:
                print(f"Failed to process job {idx} after {MAX_RETRIES} retries")
                applications.append({
                    "Job Link": job_url if 'job_url' in locals() else "Unknown",
                    "Status": "Failed after retries",
                    "Chatbot Used": "No",
                    "Page": current_page,
                    "Timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                })

        return True

    except Exception as e:
        print(f"Critical error processing page {current_page}: {str(e)}")
        return False

def main():
    try:
        driver, wait = setup_driver()
        handle_authentication(driver, wait)
        configure_search(wait)
        apply_filters(wait)

        current_page = 1
        while True:
            print(f"\n{'='*40}\nProcessing page {current_page}\n{'='*40}")
            if not process_job_page(driver, wait, current_page):
                print(f"Stopped processing at page {current_page}")
                break

            # Enhanced pagination handling
            try:
                next_btn = wait.until(EC.presence_of_element_located(
                    (By.XPATH, "//a[contains(., 'Next') and not(contains(@class, 'disabled'))]"))
                )
                if next_btn.is_enabled():
                    next_btn.click()
                    print(f"Navigated to page {current_page + 1}")
                    current_page += 1
                    # Wait for page stability
                    WebDriverWait(driver, PAGE_LOAD_TIMEOUT).until(
                        lambda d: d.execute_script("return document.readyState") == "complete"
                    )
                    time.sleep(2)  # Additional stabilization time
                else:
                    print("Next button is disabled")
                    break
            except TimeoutException:
                print("No more pages available")
                break

    except Exception as e:
        print(f"Main process error: {str(e)}")
    finally:
        # Ensure data preservation
        try:
            df = pd.DataFrame(applications)
            df.to_excel("job_applications.xlsx", index=False)
            print("\n✅ Data saved to job_applications.xlsx")
        except Exception as e:
            print(f"Error saving data: {str(e)}")
        
        if 'driver' in locals():
            driver.quit()
            print("Browser closed")

def handle_linkedin_form(driver):
    """Handle LinkedIn application form fields"""
    try:
        # Handle email dropdown
        try:
            email_select = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "select[data-test-text-entity-list-form-select]"))
            )
            # Select the first non-default email option
            email_options = email_select.find_elements(By.TAG_NAME, "option")
            for option in email_options:
                if "gmail" in option.get_attribute("value").lower():
                    option.click()
                    print("Selected email address")
                    break
        except Exception as e:
            print(f"Email selection failed: {str(e)}")

        # Handle country code dropdown
        try:
            country_select = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "select[id*='phoneNumber-country']"))
            )
            # Select India (+91)
            country_options = country_select.find_elements(By.TAG_NAME, "option")
            for option in country_options:
                if "India (+91)" in option.get_attribute("value"):
                    option.click()
                    print("Selected India (+91) country code")
                    break
        except Exception as e:
            print(f"Country code selection failed: {str(e)}")        # Handle mobile phone number input
        try:
            phone_input = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "input[id*='phoneNumber-nationalNumber']"))
            )
            phone_input.clear()
            phone_input.send_keys("8872123623")  # Replace with your actual phone number
            print("Entered phone number")
        except Exception as e:
            print(f"Phone number input failed: {str(e)}")

        return True
    except Exception as e:
        print(f"LinkedIn form handling failed: {str(e)}")
        return False

def debug_radio_buttons(driver):
    """Debug function to analyze radio button structure"""
    try:
        print("\n=== RADIO BUTTON DEBUG INFO ===")

        # Check for chatbot container
        chatbot_containers = driver.find_elements(By.CSS_SELECTOR, ".chatbot_MessageContainer, .singleselect-radiobutton")
        print(f"Chatbot containers found: {len(chatbot_containers)}")

        # Check for radio button containers
        radio_containers = driver.find_elements(By.CSS_SELECTOR, ".singleselect-radiobutton, .ssrc__radio-btn-container")
        print(f"Radio containers found: {len(radio_containers)}")

        # Get all radio buttons
        all_radios = driver.find_elements(By.CSS_SELECTOR, "input[type='radio']")
        print(f"Total radio buttons found: {len(all_radios)}")

        for i, radio in enumerate(all_radios):
            try:
                print(f"\nRadio Button {i+1}:")
                print(f"  ID: {radio.get_attribute('id')}")
                print(f"  Name: {radio.get_attribute('name')}")
                print(f"  Value: {radio.get_attribute('value')}")
                print(f"  Class: {radio.get_attribute('class')}")
                print(f"  Displayed: {radio.is_displayed()}")
                print(f"  Enabled: {radio.is_enabled()}")
                print(f"  Location: {radio.location}")
                print(f"  Size: {radio.size}")

                # Check for associated label
                radio_id = radio.get_attribute('id')
                if radio_id:
                    labels = driver.find_elements(By.CSS_SELECTOR, f"label[for='{radio_id}']")
                    if labels:
                        label = labels[0]
                        print(f"  Label text: '{label.text}'")
                        print(f"  Label displayed: {label.is_displayed()}")
                        print(f"  Label enabled: {label.is_enabled()}")
                        print(f"  Label location: {label.location}")

            except Exception as e:
                print(f"  Error getting details: {e}")

        # Check page source for radio button HTML
        page_source = driver.page_source
        if "singleselect-radiobutton" in page_source:
            print("\n✓ Single select radio button container found in page source")
        if "ssrc__radio" in page_source:
            print("✓ SSRC radio class found in page source")

        print("=== END DEBUG INFO ===\n")

    except Exception as e:
        print(f"Debug function failed: {e}")

def robust_click_radio(driver):
    """Robustly click the first enabled radio button with enhanced strategies"""
    try:
        # Wait for radio buttons to be present
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='radio']"))
        )

        # Strategy 1: Try clicking radio inputs directly
        radios = driver.find_elements(By.CSS_SELECTOR, "input[type='radio']:not([disabled])")
        print(f"Found {len(radios)} radio buttons")

        for i, radio in enumerate(radios):
            try:
                # Scroll element into view
                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", radio)
                time.sleep(0.5)

                if radio.is_displayed() and radio.is_enabled():
                    # Try normal click first
                    try:
                        WebDriverWait(driver, 5).until(EC.element_to_be_clickable(radio))
                        radio.click()
                        print(f"Clicked radio button {i+1} (normal click)")
                        return True
                    except Exception as e1:
                        print(f"Normal click failed: {e1}")

                        # Try JavaScript click
                        try:
                            driver.execute_script("arguments[0].click();", radio)
                            print(f"Clicked radio button {i+1} (JS click)")
                            return True
                        except Exception as e2:
                            print(f"JS click failed: {e2}")

                            # Try clicking associated label
                            radio_id = radio.get_attribute("id")
                            if radio_id:
                                try:
                                    label = driver.find_element(By.CSS_SELECTOR, f"label[for='{radio_id}']")
                                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", label)
                                    time.sleep(0.5)
                                    label.click()
                                    print(f"Clicked radio button {i+1} via label (normal click)")
                                    return True
                                except Exception as e3:
                                    try:
                                        driver.execute_script("arguments[0].click();", label)
                                        print(f"Clicked radio button {i+1} via label (JS click)")
                                        return True
                                    except Exception as e4:
                                        print(f"Label click failed: {e4}")
            except Exception as e:
                print(f"Error processing radio button {i+1}: {e}")
                continue

        # Strategy 2: Try clicking by specific selectors for Naukri chatbot
        naukri_selectors = [
            "div.ssrc__radio-btn-container input[type='radio']",
            ".singleselect-radiobutton input[type='radio']",
            "input.ssrc__radio",
            "label.ssrc__label"
        ]

        for selector in naukri_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    element = elements[0]  # Click first option
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                    time.sleep(0.5)

                    try:
                        element.click()
                        print(f"Clicked using selector: {selector} (normal click)")
                        return True
                    except Exception:
                        driver.execute_script("arguments[0].click();", element)
                        print(f"Clicked using selector: {selector} (JS click)")
                        return True
            except Exception as e:
                print(f"Selector {selector} failed: {e}")
                continue

        # Strategy 3: Try ActionChains for complex interactions
        try:
            radios = driver.find_elements(By.CSS_SELECTOR, "input[type='radio']:not([disabled])")
            if radios:
                radio = radios[0]
                actions = ActionChains(driver)
                actions.move_to_element(radio).click().perform()
                print("Clicked radio button using ActionChains")
                return True
        except Exception as e:
            print(f"ActionChains failed: {e}")

        print("All radio button click strategies failed")
        return False

    except Exception as e:
        print(f"Radio button click failed: {e}")
        return False

if __name__ == "__main__":
    main()