# from selenium import webdriver
# from selenium.webdriver.chrome.service import Service
# from selenium.webdriver.chrome.options import Options
# from webdriver_manager.chrome import ChromeDriverManager
# import pickle
# import time

# # Set up Chrome options
# chrome_options = Options()

# # Use the existing Chrome user profile
# user_data_dir = r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data"  # Update with your path
# profile_dir = "Default"  # You can change this if you're using a different profile

# # Add the user data directory and profile to the Chrome options
# chrome_options.add_argument(f"user-data-dir={user_data_dir}")
# chrome_options.add_argument(f"profile-directory={profile_dir}")

# # Initialize WebDriver with the specified options
# driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)

# # Go to the website where you're logged in
# driver.get("https://www.glassdoor.co.in/Job/index.htm")

# # Wait for the page to load
# time.sleep(10)

# # Extract cookies and save them to a file
# cookies = driver.get_cookies()
# pickle.dump(cookies, open("cookies.pkl", "wb"))

# # Close the browser
# driver.quit()

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
import pickle
import time

# Set up Chrome options
chrome_options = Options()

# Path to your Chrome executable
chrome_options.binary_location = r"C:\Program Files\Google\Chrome\Application\chrome.exe"  # Update with your path

# Add stability and automation flags
chrome_options.add_argument("--no-sandbox")
chrome_options.add_argument("--disable-dev-shm-usage")
chrome_options.add_argument("--disable-blink-features=AutomationControlled")
chrome_options.add_argument("--remote-debugging-port=9222")
chrome_options.add_argument("--disable-gpu")
chrome_options.add_argument("--disable-extensions")
chrome_options.add_argument("--disable-software-rasterizer")
chrome_options.add_argument("--disable-background-networking")
chrome_options.add_argument("--disable-background-timer-throttling")
chrome_options.add_argument("--disable-backgrounding-occluded-windows")
chrome_options.add_argument("--disable-breakpad")
chrome_options.add_argument("--disable-component-update")
chrome_options.add_argument("--disable-features=AudioServiceOutOfProcess")
chrome_options.add_argument("--disable-hang-monitor")
chrome_options.add_argument("--disable-popup-blocking")
chrome_options.add_argument("--disable-prompt-on-repost")
chrome_options.add_argument("--disable-renderer-backgrounding")
chrome_options.add_argument("--disable-sync")
chrome_options.add_argument("--force-color-profile=srgb")
chrome_options.add_argument("--metrics-recording-only")
chrome_options.add_argument("--no-first-run")
chrome_options.add_argument("--safebrowsing-disable-auto-update")
chrome_options.add_argument("--enable-automation")
chrome_options.add_argument("--password-store=basic")
chrome_options.add_argument("--use-mock-keychain")



# Use the existing Chrome user profile
user_data_dir = r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data"  # Update with your path
profile_dir = "Default"  # You can change this if you're using a different profile

# Add the user data directory and profile to the Chrome options
chrome_options.add_argument(f"user-data-dir={user_data_dir}")
chrome_options.add_argument(f"profile-directory={profile_dir}")

# Debugging information
print("Chrome binary location:", chrome_options.binary_location)
print("Chrome options:", chrome_options.arguments)

# Print Chrome version and initialize WebDriver
print(f"Installed Chrome version: 132.0.6834.197")
try:
    # Ensure Chrome is not already running
    import psutil
    for proc in psutil.process_iter(['pid', 'name']):
        if proc.info['name'] == 'chrome.exe':
            proc.kill()
    
    # Use ChromeDriverManager with explicit version matching
    driver_version = ChromeDriverManager().install()
    print(f"Installed ChromeDriver version: {driver_version}")
    
    # Initialize WebDriver with additional error handling
    driver = webdriver.Chrome(
        service=Service(driver_version),
        options=chrome_options
    )
    print("WebDriver initialized successfully")
except Exception as e:
    print(f"Error initializing WebDriver: {str(e)}")
    # Try again with a fresh profile if first attempt fails
    try:
        chrome_options.add_argument("--user-data-dir=/tmp/chrome-profile")
        driver = webdriver.Chrome(
            service=Service(driver_version),
            options=chrome_options
        )
        print("WebDriver initialized successfully with temporary profile")
    except Exception as e2:
        print(f"Second attempt failed: {str(e2)}")
        raise



# Go to the website where you're logged in
driver.get("https://www.glassdoor.co.in/Job/index.htm")

# Wait for the page to load with detailed logging
print("Waiting for page to load...")
start_time = time.time()
time.sleep(5)  # Reduced from 10 to 5 seconds
print(f"Page load wait completed in {time.time() - start_time:.2f} seconds")


# Extract cookies and save them to a file with detailed logging
print("Extracting cookies...")
cookies = driver.get_cookies()
print(f"Found {len(cookies)} cookies")
pickle.dump(cookies, open("cookies.pkl", "wb"))
print("Cookies saved to cookies.pkl")


# Close the browser with detailed logging
print("Closing browser...")
driver.quit()
print("Browser closed successfully")
