import time
import pickle
import pandas as pd
import psutil
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException


class GlassdoorJobAutomation:
    def __init__(self):
        self.setup_driver()
        self.wait = WebDriverWait(self.driver, 10)
        self.applications = []

    def setup_driver(self):
        chrome_options = Options()
        chrome_options.binary_location = r"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-popup-blocking")
        chrome_options.add_argument("--enable-automation")
        chrome_options.add_argument(f"user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data")
        chrome_options.add_argument("--profile-directory=Default")
        
        for proc in psutil.process_iter(['pid', 'name']):
            if proc.info['name'] == 'chrome.exe':
                proc.kill()
        
        driver_version = ChromeDriverManager().install()
        self.driver = webdriver.Chrome(service=Service(driver_version), options=chrome_options)
        self.driver.maximize_window()
        print("WebDriver initialized successfully")

    def load_cookies(self):
        try:
            self.driver.get("https://www.glassdoor.co.in/Job/index.htm")
            # time.sleep(5)
            # cookies = pickle.load(open("cookies.pkl", "rb"))
            # for cookie in cookies:
            #     self.driver.add_cookie(cookie)
            # self.driver.refresh()
            # print("Cookies loaded successfully")
        except Exception as e:
            print(f"Failed to load cookies: {str(e)}")

    def search_jobs(self, keywords, location):
        try:
            time.sleep(5)
            self.driver.get("https://www.glassdoor.co.in/Job/index.htm")
            # search_field = WebDriverWait(self.driver, 10).until(EC.presence_of_element_located((By.ID, "searchBar-jobTitle")))
            # search_field.send_keys(keywords)
            
            # location_field = WebDriverWait(self.driver, 10).until(EC.presence_of_element_located((By.ID, "searchBar-location")))
            # location_field.send_keys(Keys.CONTROL + "a")
            # location_field.send_keys(location + Keys.RETURN)

            # time.sleep(10)
            # WebDriverWait(self.driver, 10).until(EC.presence_of_element_located((By.CLASS_NAME, "JobsList_jobsList__lqjTr")))
            print("Job search completed successfully")
            return True
        except Exception as e:
            print(f"Search failed: {str(e)}")
            return False
    
    # def apply_to_jobs(self):
    #     try:
    #         # Wait for the job listings to be visible
    #         job_listings = self.wait.until(EC.presence_of_all_elements_located((By.CLASS_NAME, "JobsList_jobListItem__wjTHv")))
    #         print(f"Found {len(job_listings)} job listings")
            
    #         for job in job_listings:
    #             try:
    #                 WebDriverWait(self.driver, 10).until(EC.element_to_be_clickable(job))
    #                 job.click()
    #                 time.sleep(2)
    #                 try:
    #                     easy_apply_button = WebDriverWait(self.driver, 5).until(
    #                         EC.presence_of_element_located((By.XPATH, "//button[@data-test='easyApply']"))
    #                     )
    #                     # If Easy Apply button is found, click it to open a new tab
    #                     easy_apply_button.click()
    #                     time.sleep(2)
    #                     print(f"Applied to job: {job.get_attribute('data-jobid')}")
    #                     self.record_application("Applied")  # Record successful application
    #                     self.driver.switch_to.window(self.driver.window_handles[1])

    #                     self.driver.close()
    #                     self.driver.switch_to.window(self.driver.window_handles[0])

    #                 except Exception as e:
    #                     print(f"Job {job.get_attribute('data-jobid')} does not have Easy Apply.")
                        
    #                     try:
    #                         already_applied = WebDriverWait(self.driver, 3).until(
    #                             EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Already Applied')]"))
    #                         )
    #                         print(f"Job {job.get_attribute('data-jobid')} has already been applied.")
    #                         self.record_application("Already Applied")  # Mark as already applied

    #                     except Exception:
    #                         try:
    #                             apply_on_company_site = WebDriverWait(self.driver, 3).until(
    #                                 EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Apply on Company Site')]"))
    #                             )
    #                             print(f"Job {job.get_attribute('data-jobid')} needs to be applied on the company site.")
    #                             self.record_application("Apply on Company Site")  # Mark as apply on company site
    #                         except Exception:
    #                             print(f"Job {job.get_attribute('data-jobid')} does not have Easy Apply or Apply on Company Site.")
    #                             self.record_application("Failed")  # Mark as failed
    #                 self.driver.close()
    #                 self.driver.switch_to.window(self.driver.window_handles[0])

    #             except Exception as e:
    #                 print(f"Error processing job {job.get_attribute('data-jobid')}: {e}")

    #     except Exception as e:
    #         print(f"Error with job application process: {str(e)}")
    def apply_to_jobs(self):
        try:
            job_listings = self.wait.until(EC.presence_of_all_elements_located((By.CLASS_NAME, "JobsList_jobListItem__wjTHv")))
            print(f"Found {len(job_listings)} job listings")
            time.sleep(2)

            for index, job in enumerate(job_listings):
                try:
                    job_id = job.get_attribute('data-jobid')
                    WebDriverWait(self.driver, 5).until(EC.element_to_be_clickable(job)).click()
                    print(f"Applying to job {job_id}")
                    time.sleep(2)

                    try:
                        easy_apply_button = WebDriverWait(self.driver, 5).until(
                            EC.presence_of_element_located(By.XPATH, '//button[@data-test="easyApply" and @aria-disabled="false"]'))
                        easy_apply_button.click()
                        print("Easy Apply button clicked.")
                        time.sleep(2)
                    except TimeoutException:
                        print(f"Job {job_id} does not have Easy Apply.")
                        self.handle_non_easy_apply(job_id)
                        continue

                    self.driver.switch_to.window(self.driver.window_handles[-1])

                    if not self.select_resume():
                        continue

                    if not self.fill_job_details():
                        continue

                    if not self.handle_experience_section():
                        continue

                    self.complete_application_process(job_id)

                    self.driver.close()
                    self.driver.switch_to.window(self.driver.window_handles[0])

                except Exception as e:
                    print(f"Error processing job {job_id}: {e}")
                    self.driver.refresh()
                    continue

        except Exception as e:
            print(f"Critical error in job application process: {e}")
            self.driver.quit()


    def select_resume(self):
        try:
            resume_radio_button = WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.XPATH, "//label[contains(@for, 'ihl-useId')]//span[contains(text(),'DEEPAK GARG RESUME.pdf')]"))
            )
            resume_radio_button.click()
            print("Resume selected.")
            return True
        except TimeoutException:
            print("Error: Resume selection failed.")
            self.driver.close()
            self.driver.switch_to.window(self.driver.window_handles[0])
            return False


    def fill_job_details(self):
        try:
            job_title_input = self.driver.find_element(By.XPATH, '//input[@name="jobTitle"]')
            company_input = self.driver.find_element(By.XPATH, '//input[@name="company"]')
            job_title_input.send_keys("Data Science & ML Developer")
            company_input.send_keys("Appsquadz")
            return True
        except Exception:
            print("Error: Failed to fill job details.")
            self.driver.close()
            self.driver.switch_to.window(self.driver.window_handles[0])
            return False


    def handle_experience_section(self):
        try:
            yes_option = self.driver.find_element(By.XPATH, '//input[@type="radio" and @value="YES"]')
            yes_option.click()
            year_input = self.driver.find_element(By.XPATH, '//input[@name="yearsExperience"]')
            year_input.clear()
            year_input.send_keys("1")
            return True
        except Exception:
            print("Error: Experience section failed.")
            self.driver.close()
            self.driver.switch_to.window(self.driver.window_handles[0])
            return False


    def complete_application_process(self, job_id):
        while True:
            try:
                submit_button = self.driver.find_element(By.XPATH, '//button[contains(text(), "Submit your application")]')
                submit_button.click()
                print(f"Successfully applied to job {job_id}.")
                self.record_application("Applied")
                break
            except NoSuchElementException:
                try:
                    continue_button = self.driver.find_element(By.XPATH, '//button[contains(text(), "Continue")]')
                    continue_button.click()
                except NoSuchElementException:
                    print("No more continue buttons. Exiting loop.")
                    break


    def handle_non_easy_apply(self, job_id):
        try:
            already_applied = WebDriverWait(self.driver, 2).until(
                EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Already Applied')]"))
            )
            print(f"Job {job_id} has already been applied.")
            self.record_application("Already Applied")
        except TimeoutException:
            try:
                apply_on_company_site = WebDriverWait(self.driver, 2).until(
                    EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Apply on Company Site')]"))
                )
                print(f"Job {job_id} needs to be applied on the company site.")
                self.record_application("Apply on Company Site")
            except TimeoutException:
                print(f"Job {job_id} does not have Easy Apply or Apply on Company Site.")
                self.record_application("Failed")


    def record_application(self, status):
        try:
            job_title = self.wait.until(EC.presence_of_element_located((By.CLASS_NAME, "jobTitle"))).text
            company = self.wait.until(EC.presence_of_element_located((By.CLASS_NAME, "employerName"))).text
        except:
            job_title = "Unknown"
            company = "Unknown"
        
        self.applications.append({
            'Job Title': job_title,
            'Company': company,
            'Status': status,
            'Timestamp': pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
        })
    def save_results(self):
        df = pd.DataFrame(self.applications)
        df.to_excel("glassdoor_applications.xlsx", index=False)
        print("Results saved to glassdoor_applications.xlsx")

    def run(self, keywords, location):
        self.load_cookies()
        if self.search_jobs(keywords, location):
            self.apply_to_jobs()
        self.save_results()
        self.driver.quit()

if __name__ == "__main__":
    bot = GlassdoorJobAutomation()
    #bot.run("Data Analyst,Data Science,Python ", "Gurugram,Noida,New Delhi,Pune")
    bot.run(" ", "")
