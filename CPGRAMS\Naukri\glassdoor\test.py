# import time
# from selenium import webdriver
# from selenium.webdriver.common.by import By
# from selenium.webdriver.support.ui import WebDriverWait
# from selenium.webdriver.support import expected_conditions as EC

# class GlassdoorAutomation:
#     def __init__(self, config):
#         self.driver = self.setup_driver()
#         self.wait = WebDriverWait(self.driver, 20)
#         self.config = config
    
#     def setup_driver(self):
#         options = webdriver.ChromeOptions()

#         # Specify the path to the Brave executable if needed
#         options.add_argument("--start-maximized")
#         options.add_argument("--disable-notifications")
#         options.add_argument("--disable-blink-features=AutomationControlled")
        
#         # Create WebDriver instance using ChromeDriver (works with <PERSON> as well)
#         driver = webdriver.Chrome(options=options)
#         driver.set_page_load_timeout(30)
#         return driver

#     def login(self, iterable_token):
#         try:
#             self.driver.get("https://www.glassdoor.com")
#             # Inject the iterableToken into local storage
#             self.driver.execute_script(f"window.localStorage.setItem('iterableToken', '{iterable_token}')")
            
#             # Wait to ensure token is stored in localStorage
#             time.sleep(2)
            
#             # Check if the token was successfully injected
#             token_check = self.driver.execute_script("return window.localStorage.getItem('iterableToken');")
#             print(f"Token in localStorage: {token_check}")  # Debug print to see if token is correctly set
            
#             # Refresh the page to apply the token
#             self.driver.refresh()
            
#             # Wait for the page to fully load after the refresh
#             time.sleep(10)

#             # Check for login success by verifying if the job search field is available
#             try:
#                 self.wait.until(EC.presence_of_element_located((By.ID, "KeywordSearch-input")))  # Example of a job search field to check login success
#                 print("Login successful")
#                 return True
#             except:
#                 print("Login failed")
#                 return False

#         except Exception as e:
#             print(f"An error occurred during login: {str(e)}")
#             return False

#     def search_jobs(self, keywords, location):
#         # This function can be expanded for actual job searching logic
#         try:
#             print(f"Searching for jobs with keywords '{keywords}' in location '{location}'...")
#             self.driver.get("https://www.glassdoor.co.in/Job/index.htm")
#             self.wait.until(EC.presence_of_element_located((By.ID, "KeywordSearch-input")))
#             keyword_input = self.driver.find_element(By.ID, "KeywordSearch-input")
#             location_input = self.driver.find_element(By.ID, "LocationSearch-input")
#             keyword_input.clear()
#             location_input.clear()
#             keyword_input.send_keys(keywords)
#             location_input.send_keys(location)
#             location_input.submit()
#             time.sleep(5)  # Allow the page to load the results
#             print("Job search completed.")
#             return True
#         except Exception as e:
#             print(f"An error occurred during job search: {str(e)}")
#             return False

#     def apply_to_jobs(self):
#         # Placeholder function to apply to jobs
#         try:
#             print("Applying to jobs...")
#         except Exception as e:
#             print(f"An error occurred while applying: {str(e)}")

#     def save_results(self):
#         # Add logic to save results (e.g., saving applied jobs)
#         try:
#             print("Saving results...")
#         except Exception as e:
#             print(f"An error occurred while saving results: {str(e)}")

#     def run(self, keywords, location):
#         if self.login(iterable_token):
#             if self.search_jobs(keywords, location):
#                 self.apply_to_jobs()
#             self.save_results()
#         self.driver.quit()


# # Usage example
# if __name__ == "__main__":
#     iterable_token = "eyJraWQiOiJqd3RDb21tb25zLnVzZXJFbmdhZ2VtZW50LndlYk5vdGlmaWNhdGlvbnNKV1RJdGVyYWJsZUtleSIsInR5cCI6IkpXVCIsImFsZyI6IkhTMjU2In0.********************************************************************************************.WBgNBNiVBDG-PamNH_TdzOmjbgttP7thBqHr6sopZO0"  # Replace with your token
#     config = {
#         'personal_info': {
#             'first_name': "Deepak",
#             'last_name': "Garg",
#             'email': "<EMAIL>",
#             'phone': "+91 7838630502",
#             'address': "Faridabad, Haryana",
#             'headline': "Aspiring Data Scientist",
#             'cover_letter': "Aspiring Data Scientist with experience in Data Analysis...",
#             'resume_path': "C:/Users/<USER>/CPGRAMS/Naukri/DEEPAK GARG RESUME.pdf"
#         },
#         'education': {
#             'school': "Bennett University",
#             'degree': "MCA",
#             'major': "Data Science",
#             'dates': {
#                 'start': {"month": "August", "day": "1", "year": "2023"},
#                 'end': {"month": "May", "day": "31", "year": "2025"}
#             }
#         },
#         'experience': [
#             {
#                 'title': "Data Science & ML Developer",
#                 'company': "Appsquadz",
#                 'industry': "Data Science",
#                 'dates': {
#                     'start': {"month": "January", "year": "2025"},
#                     'end': {"month": "Present", "year": ""}
#                 }
#             }
#         ]
#     }  # You can set the config with additional personal info or other settings
#     automation = GlassdoorAutomation(config)
#     automation.run(keywords="Data Scientist", location="India")

import time
import pickle
import pandas as pd
import psutil
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.action_chains import ActionChains
import random


class GlassdoorJobAutomation:
    def __init__(self):
        self.setup_driver()
        self.wait = WebDriverWait(self.driver, 10)
        self.applications = []

    def setup_driver(self):
        chrome_options = Options()
        chrome_options.binary_location = r"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-popup-blocking")
        chrome_options.add_argument("--enable-automation")
        chrome_options.add_argument(f"user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data")
        chrome_options.add_argument("--profile-directory=Default")
        chrome_options.add_argument("--disable-infobars")
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option("useAutomationExtension", False)
        chrome_options.add_argument("--no-sandbox")


        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--remote-debugging-port=9222")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-software-rasterizer")
        chrome_options.add_argument("--disable-background-networking")
        chrome_options.add_argument("--disable-background-timer-throttling")
        chrome_options.add_argument("--disable-backgrounding-occluded-windows")
        chrome_options.add_argument("--disable-breakpad")
        chrome_options.add_argument("--disable-component-update")
        chrome_options.add_argument("--disable-features=AudioServiceOutOfProcess")
        chrome_options.add_argument("--disable-hang-monitor")
        chrome_options.add_argument("--disable-popup-blocking")
        chrome_options.add_argument("--disable-prompt-on-repost")
        chrome_options.add_argument("--disable-renderer-backgrounding")
        chrome_options.add_argument("--disable-sync")
        chrome_options.add_argument("--force-color-profile=srgb")
        chrome_options.add_argument("--metrics-recording-only")
        chrome_options.add_argument("--no-first-run")
        chrome_options.add_argument("--safebrowsing-disable-auto-update")
        chrome_options.add_argument("--enable-automation")
        chrome_options.add_argument("--password-store=basic")
        chrome_options.add_argument("--use-mock-keychain")
        # Set user agent
        chrome_options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36")
        
        for proc in psutil.process_iter(['pid', 'name']):
            if proc.info['name'] == 'chrome.exe':
                proc.kill()
        
        driver_version = ChromeDriverManager().install()
        self.driver = webdriver.Chrome(service=Service(driver_version), options=chrome_options)
        self.driver.maximize_window()
        print("WebDriver initialized successfully")

    def load_cookies(self):
        try:
            self.driver.get("https://www.glassdoor.co.in/Job/index.htm")
            # time.sleep(5)
            # cookies = pickle.load(open("cookies.pkl", "rb"))
            # for cookie in cookies:
            #     self.driver.add_cookie(cookie)
            # self.driver.refresh()
            # print("Cookies loaded successfully")
        except Exception as e:
            print(f"Failed to load cookies: {str(e)}")

    def human_like_delay(self, min=1, max=3):
        time.sleep(random.uniform(min, max))

    def move_mouse(self, element):
        ActionChains(self.driver)\
            .move_to_element(element)\
            .pause(random.uniform(0.5, 1.5))\
            .perform()

    def search_jobs(self, keywords, location):
        try:
            #time.sleep(5)
            self.driver.get("https://www.glassdoor.co.in/Job/index.htm")
            # search_field = WebDriverWait(self.driver, 10).until(EC.presence_of_element_located((By.ID, "searchBar-jobTitle")))
            # search_field.send_keys(keywords)
            
            # location_field = WebDriverWait(self.driver, 10).until(EC.presence_of_element_located((By.ID, "searchBar-location")))
            # location_field.send_keys(Keys.CONTROL + "a")
            # location_field.send_keys(location + Keys.RETURN)

            # time.sleep(10)
            # WebDriverWait(self.driver, 10).until(EC.presence_of_element_located((By.CLASS_NAME, "JobsList_jobsList__lqjTr")))
            print("Job search completed successfully")
            return True
        except Exception as e:
            print(f"Search failed: {str(e)}")
            return False

    def apply_to_jobs(self):
        try:
            self.human_like_delay()
            job_listings = self.wait.until(EC.presence_of_all_elements_located((By.CLASS_NAME, "JobsList_jobListItem__wjTHv")))
            print(f"Found {len(job_listings)} job listings")
            time.sleep(2)
            
            for job in job_listings:
                try:
                    WebDriverWait(self.driver, 10).until(EC.element_to_be_clickable(job))
                    self.human_like_delay()
                    job.click()
                    print(f"Applying to job ")
                    try:
                        easy_apply_button = WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, '//button[@data-test="easyApply" and @aria-disabled="false"]')))
                        self.human_like_delay()
                        easy_apply_button.click()
                        print("Easy Apply button clicked.")
                        # Switch to new tab and verify content
                        WebDriverWait(self.driver, 10).until(lambda d: len(d.window_handles) > 1)
                        self.driver.switch_to.window(self.driver.window_handles[1])
                        
                        if self.select_resume():
                            self.driver.switch_to.window(self.driver.window_handles[-1])
                            # Get all the "Continue" buttons
                            # Wait for the specific "Continue" button to be clickable
                            self.click_continue_button()
                            print("Continue button clicked.")
                        elif self.is_verification_required():
                            print("Human verification required. Pausing for manual resolution.")
                            input("Press Enter after completing verification...")
                            time.sleep(5) 
                        else:
                            continue

                    except TimeoutException:
                        print(f"Job does not have Easy Apply.")
                        self.handle_non_easy_apply()
                        self.human_like_delay()
                        continue
                        
                except Exception as e:
                    print(f"Error while applying to job: {e}")
                    self.human_like_delay()
                    continue  # Continue with the next job in case of any other errors
        except Exception as e:
            print(f"Error while loading job listings: {e}")

                    #self.driver.switch_to.window(self.driver.window_handles[-1])

                    # if not self.fill_job_details():
                    #     continue

                    # if not self.handle_experience_section():
                    #     continue

                    # self.complete_application_process(job)

                    # self.driver.close()
                    # self.driver.switch_to.window(self.driver.window_handles[0])

    def select_resume(self):
        try:
            resume_radio_button = WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.XPATH, "//label[contains(@for, 'ihl-useId')]//span[contains(text(),'DEEPAK GARG RESUME.pdf')]"))
            )
            resume_radio_button.click()
            print("Resume selected.")
            return True
        except TimeoutException:
            print("Error: Resume selection failed.")
            self.driver.close()
            self.driver.switch_to.window(self.driver.window_handles[0])
            return False
        
    def click_continue_button(self):
        try:
            # Wait until the button is present and clickable
            continue_button = WebDriverWait(self.driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "(//button[@data-testid='continue-button'])[5]")))
            self.driver.execute_script("arguments[0].scrollIntoView();", continue_button)
            continue_button.click()

            print("Continue button clicked successfully")
        except Exception as e:
            print(f"Error clicking the continue button: {e}")
            # Fallback: Try clicking using JavaScript
            try:
                self.driver.execute_script("arguments[0].click();", continue_button)
                print("Continue button clicked successfully using JavaScript")
            except Exception as js_error:
                print(f"JavaScript click failed: {js_error}")



    def fill_job_details(self):
        try:
            job_title_input = self.driver.find_element(By.XPATH, '//input[@name="jobTitle"]')
            company_input = self.driver.find_element(By.XPATH, '//input[@name="company"]')
            job_title_input.send_keys("Data Science & ML Developer")
            company_input.send_keys("Appsquadz")
            return True
        except Exception:
            print("Error: Failed to fill job details.")
            self.driver.close()
            self.driver.switch_to.window(self.driver.window_handles[0])
            return False
        
    def is_verification_required(self):
        try:
            # Check for common verification elements
            WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.XPATH, "//h2[contains(text(), 'Verify') or contains(text(), 'CAPTCHA')]"))
            )
            print("Verification required.")
            self.handle_verification()

        except TimeoutException:
            return False

    def handle_verification(self):
        # if self.is_verification_required():
            try:
                # Locate the checkbox and click it
                checkbox = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//label[contains(@class, 'cb-lb')]/input[@type='checkbox']"))
                )
                checkbox.click()
                print("Verification checkbox clicked successfully")
            except Exception as e:
                print(f"Error clicking the verification checkbox: {e}")

        
    def handle_experience_section(self):
        try:
            yes_option = self.driver.find_element(By.XPATH, '//input[@type="radio" and @value="YES"]')
            yes_option.click()
            year_input = self.driver.find_element(By.XPATH, '//input[@name="yearsExperience"]')
            year_input.clear()
            year_input.send_keys("1")
            return True
        except Exception:
            print("Error: Experience section failed.")
            self.driver.close()
            self.driver.switch_to.window(self.driver.window_handles[0])
            return False


    def complete_application_process(self, job_id):
        while True:
            try:
                submit_button = self.driver.find_element(By.XPATH, '//button[contains(text(), "Submit your application")]')
                submit_button.click()
                print(f"Successfully applied to job {job_id}.")
                self.record_application("Applied")
                break
            except NoSuchElementException:
                try:
                    continue_button = self.driver.find_element(By.XPATH, '//button[contains(text(), "Continue")]')
                    continue_button.click()
                except NoSuchElementException:
                    print("No more continue buttons. Exiting loop.")
                    break

    def handle_non_easy_apply(self):
        try:
            # Check for the "Already Applied" button
            already_applied = WebDriverWait(self.driver, 2).until(
                EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Already Applied')]"))
            )
            print(f"checking if the Job has already been applied.")
            self.record_application("Already Applied")
            
        except TimeoutException:
            # If "Already Applied" button is not present, check for "Apply on Company Site"
            try:
                apply_on_company_site = WebDriverWait(self.driver, 2).until(
                    EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Apply on Company Site')]"))
                )
                print(f"Job needs to be applied on the company site.")
                self.record_application("Apply on Company Site")
            
            except TimeoutException:
                # If neither button is found, handle this case
                print(f"Job has no Easy Apply or Apply on Company Site option.")
                self.record_application("Failed")
                
        except Exception as e:
            # Catch any other exceptions that occur after checking both buttons
            print(f"Not able to understand the type of button, so moving to next job. Error: {str(e)}")
            self.record_application("Failed")

    def record_application(self, status):
        try:
            job_title = self.wait.until(EC.presence_of_element_located((By.CLASS_NAME, "jobTitle"))).text
            company = self.wait.until(EC.presence_of_element_located((By.CLASS_NAME, "employerName"))).text
        except:
            job_title = "Unknown"
            company = "Unknown"
        
        self.applications.append({
            'Job Title': job_title,
            'Company': company,
            'Status': status,
            'Timestamp': pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
        })
    def save_results(self):
        df = pd.DataFrame(self.applications)
        df.to_excel("glassdoor_applications.xlsx", index=False)
        print("Results saved to glassdoor_applications.xlsx")

    def run(self, keywords, location):
        self.load_cookies()
        if self.search_jobs(keywords, location):
            self.apply_to_jobs()
        self.save_results()
        self.driver.quit()

if __name__ == "__main__":
    bot = GlassdoorJobAutomation()
    #bot.run("Data Analyst,Data Science,Python ", "Gurugram,Noida,New Delhi,Pune")
    bot.run(" ", "")
