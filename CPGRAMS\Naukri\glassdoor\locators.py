from bs4 import BeautifulSoup
import json
import os

def extract_locators(html_file):
    with open(html_file, "r", encoding="utf-8") as file:
        soup = BeautifulSoup(file, "html.parser")
    
    locators = {"ids": [], "classes": [], "tags": [], "css_selectors": []}
    
    # Extract IDs
    for tag in soup.find_all(attrs={"id": True}):
        locators["ids"].append(tag["id"])
    
    # Extract Classes
    for tag in soup.find_all(attrs={"class": True}):
        for cls in tag["class"]:
            if cls not in locators["classes"]:
                locators["classes"].append(cls)
    
    # Extract Unique Tags
    tag_counts = {}
    for tag in soup.find_all():
        tag_name = tag.name
        tag_counts[tag_name] = tag_counts.get(tag_name, 0) + 1
    locators["tags"] = [tag for tag, count in tag_counts.items() if count > 2]  # Common tags
    
    # Extract CSS Selectors (id and class-based)
    for tag in soup.find_all():
        if "id" in tag.attrs:
            locators["css_selectors"].append(f"#{tag['id']}")
        if "class" in tag.attrs:
            locators["css_selectors"].extend([f".{cls}" for cls in tag["class"]])
    
    # Remove duplicates
    for key in locators:
        locators[key] = list(set(locators[key]))
    
    return locators

def save_locators_to_json(locators, output_file="locators.json"):
    with open(output_file, "w", encoding="utf-8") as file:
        json.dump(locators, file, indent=4, ensure_ascii=False)
    print(f"Locators saved to {output_file}")

if __name__ == "__main__":
    html_file = "index.html"  # Change this if the file has a different name
    if os.path.exists(html_file):
        locators = extract_locators(html_file)
        save_locators_to_json(locators)
    else:
        print("HTML file not found!")
