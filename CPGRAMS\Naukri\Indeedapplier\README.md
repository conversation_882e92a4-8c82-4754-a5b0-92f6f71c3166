# Automatic Applier for Indeed Jobs

## Setup

* First of all, you need to have  the `chromedriver` executable in your path. you can get it from [here](https://chromedriver.chromium.org/downloads).

* Then do ``` pip install selenium ``` to install the selenium package.

* Fill ```config.py``` with all your relevant details.

## Running

* Run the main script using ```python3 apply.py```

* Head to the browser that pops up and login into your indeed account. go to the Find jobs section and search for your Job title and location.

* Make sure that you close every dialogue you see on the screen, including the "Allow cookies" dialogue.

* Go back to the shell running your sciprt and hit Enter to start the automatic application process.

* Interrupt with ```Ctrl+c``` when you're done.