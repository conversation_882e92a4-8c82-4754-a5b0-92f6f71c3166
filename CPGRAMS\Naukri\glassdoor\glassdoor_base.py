from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException
import time
import json
import pandas as pd
import config # Import your config file

class GlassdoorAutomation:
    def __init__(self):
        self.driver = self.setup_driver()
        self.wait = WebDriverWait(self.driver, 20)
        self.applications = []
        self.config = config
    
    def setup_driver(self):
        options = webdriver.ChromeOptions()

        # Specify the path to the Brave executable
        #options.binary_location = "C:/Program Files/BraveSoftware/Brave-Browser/Application/brave.exe"  # Update this with your Brave path
        
        options.add_argument("--start-maximized")
        options.add_argument("--disable-notifications")
        options.add_argument("--disable-blink-features=AutomationControlled")
        # Create WebDriver instance using ChromeDriver (works with <PERSON> as well)
        driver = webdriver.Chrome(options=options)
        driver.set_page_load_timeout(30)
        return driver

    def login(self, iterable_token):
        try:
            print("Logging in...")
            self.driver.get("https://www.glassdoor.com")
            # Inject the iterableToken into local storage
            self.driver.execute_script(f"window.localStorage.setItem('iterableToken', '{iterable_token}')")
            # Refresh the page to apply the token
            print("Refreshing the page...")
            self.driver.refresh()
            # Wait for the page to fully load after the refresh
            time.sleep(10)
            try:
                self.wait.until(EC.presence_of_element_located((By.ID, "KeywordSearch-input")))  # Example of a job search field to check login success
                print("Login successful")
                return True
            except:
                print("Login failed")
                return False

        except Exception as e:
            print(f"An error occurred during login: {str(e)}")
            return False

        # except Exception as e:
        #     print(f"An error occurred during login: {str(e)}")

            
            # sign_in_btn = self.wait.until(EC.element_to_be_clickable(
            #     (By.XPATH, "//a[contains(text(),'Sign In')]")))
            # sign_in_btn.click()

            # Handle email login
            # email_field = self.wait.until(EC.presence_of_element_located(
            #     (By.ID, "inlineUserEmail")))
            # email_field.send_keys(self.config['personal_info']['email'])

            # #Alternatively, you can use the 'data-test' attribute for targeting
            # button = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[data-test="email-form-button"]')))
            # button.click()

            # password_field = self.wait.until(EC.presence_of_element_located(
            #     (By.ID, "userPassword")))
            # password_field.send_keys("your_password" + Keys.RETURN)

            # # Wait for login completion
            # self.wait.until(EC.presence_of_element_located(
            #     (By.ID, "searchBar-jobTitle")))
            # print("Login successful")
            # return True

            time.sleep(5)
        # except Exception as e:
        #     print(f"Login failed: {str(e)}")
        #     return False

    def search_jobs(self, keywords, location):
        try:
            search_field = self.wait.until(EC.presence_of_element_located(
                (By.ID, "searchBar-jobTitle")))
            search_field.send_keys(keywords)

            location_field = self.wait.until(EC.presence_of_element_located(
                (By.ID, "searchBar-location")))
            location_field.send_keys(Keys.CONTROL + "a")
            location_field.send_keys(location + Keys.RETURN)

            # Wait for search results
            self.wait.until(EC.presence_of_element_located(
                (By.CLASS_NAME, "JobsList_jobListItem__JBBUV")))
            return True
        except Exception as e:
            print(f"Search failed: {str(e)}")
            return False

    def apply_to_jobs(self):
        try:
            job_listings = self.wait.until(EC.presence_of_all_elements_located(
                (By.CLASS_NAME, "JobsList_jobListItem__JBBUV")))

            for job in job_listings:
                job.click()
                time.sleep(2)
                
                # Switch to job details frame
                self.driver.switch_to.window(self.driver.window_handles[1])
                
                if self.handle_application():
                    self.record_application("Applied")
                else:
                    self.record_application("Failed")

                # Close tab and switch back
                self.driver.close()
                self.driver.switch_to.window(self.driver.window_handles[0])

        except Exception as e:
            print(f"Job application error: {str(e)}")

    def handle_application(self):
        try:
            apply_btn = self.wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//button[contains(., 'Apply Now')]")))
            apply_btn.click()

            # Fill personal information
            self.fill_personal_info()
            self.fill_education()
            self.fill_experience()
            self.upload_resume()

            # Submit application
            submit_btn = self.wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//button[contains(., 'Submit Application')]")))
            submit_btn.click()
            
            return True
        except Exception as e:
            print(f"Application error: {str(e)}")
            return False

    def fill_personal_info(self):
        fields = {
            'firstName': self.config['personal_info']['first_name'],
            'lastName': self.config['personal_info']['last_name'],
            'email': self.config['personal_info']['email'],
            'phoneNumber': self.config['personal_info']['phone'],
            'headline': self.config['personal_info']['headline'],
            'address': self.config['personal_info']['address']
        }

        for field_id, value in fields.items():
            element = self.wait.until(EC.presence_of_element_located(
                (By.ID, field_id)))
            element.clear()
            element.send_keys(value)

        cover_letter = self.wait.until(EC.presence_of_element_located(
            (By.ID, "coverLetterTextArea")))
        cover_letter.send_keys(self.config['personal_info']['cover_letter'])

    def fill_education(self):
        self.wait.until(EC.element_to_be_clickable(
            (By.XPATH, "//button[contains(., 'Add Education')]"))).click()

        education = self.config['education']
        self.fill_dropdown('school', education['school'])
        self.fill_dropdown('degree', education['degree'])
        self.fill_dropdown('fieldOfStudy', education['major'])

        # Fill dates
        self.select_date('startDate', education['dates']['start'])
        self.select_date('endDate', education['dates']['end'])

    def fill_experience(self):
        for exp in self.config['experience']:
            self.wait.until(EC.element_to_be_clickable(
                (By.XPATH, "//button[contains(., 'Add Experience')]"))).click()

            self.fill_dropdown('jobTitle', exp['title'])
            self.fill_dropdown('company', exp['company'])
            self.fill_dropdown('industry', exp['industry'])
            self.select_date('startDate', exp['dates']['start'])
            
            if exp['dates']['end']['month']:
                self.select_date('endDate', exp['dates']['end'])
            else:
                current_checkbox = self.wait.until(EC.element_to_be_clickable(
                    (By.XPATH, "//input[@type='checkbox' and contains(@id, 'current')]")))
                current_checkbox.click()

    def upload_resume(self):
        resume_input = self.wait.until(EC.presence_of_element_located(
            (By.XPATH, "//input[@type='file']")))
        resume_input.send_keys(self.config['personal_info']['resume_path'])

    def select_date(self, field_prefix, date):
        self.select_dropdown(f"{field_prefix}Month", date['month'])
        if 'day' in date:
            self.select_dropdown(f"{field_prefix}Day", date['day'])
        self.select_dropdown(f"{field_prefix}Year", date['year'])

    def select_dropdown(self, element_id, value):
        dropdown = self.wait.until(EC.element_to_be_clickable(
            (By.ID, element_id)))
        dropdown.send_keys(value)

    def record_application(self, status):
        try:
            job_title = self.wait.until(EC.presence_of_element_located(
                (By.CLASS_NAME, "jobTitle"))).text
            company = self.wait.until(EC.presence_of_element_located(
                (By.CLASS_NAME, "employerName"))).text
        except:
            job_title = "Unknown"
            company = "Unknown"

        self.applications.append({
            'Job Title': job_title,
            'Company': company,
            'Status': status,
            'Timestamp': pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
        })

    def save_results(self):
        df = pd.DataFrame(self.applications)
        df.to_excel("glassdoor_applications.xlsx", index=False)
        print("Results saved to glassdoor_applications.xlsx")

    def run(self, keywords, location):
        if self.login(iterable_token):
            if self.search_jobs(keywords, location):
                self.apply_to_jobs()
        self.save_results()
        self.driver.quit()

if __name__ == "__main__":
    iterable_token="eyJraWQiOiJqd3RDb21tb25zLnVzZXJFbmdhZ2VtZW50LndlYk5vdGlmaWNhdGlvbnNKV1RJdGVyYWJsZUtleSIsInR5cCI6IkpXVCIsImFsZyI6IkhTMjU2In0.********************************************************************************************.WBgNBNiVBDG-PamNH_TdzOmjbgttP7thBqHr6sopZO0"  # Replace with your token
    # automation = GlassdoorAutomation()
    # automation.login(iterable_token)
    # automation.run(
    #     keywords="Data Scientist",
    #     location="India"
    # )
    automation = GlassdoorAutomation()
    automation.run(keywords="Data Scientist", location="India")