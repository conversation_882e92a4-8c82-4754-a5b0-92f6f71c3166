import time
import pickle
import pandas as pd
import random
import pyautogui
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from fake_useragent import UserAgent

class GlassdoorJobAutomation:
    def __init__(self):
        self.setup_driver()
        self.wait = WebDriverWait(self.driver, 10)
        self.applications = []

    # def setup_driver(self):
    #     chrome_options = Options()
    #     chrome_options.add_argument("--no-sandbox")
    #     chrome_options.add_argument("--disable-dev-shm-usage")
    #     chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    #     chrome_options.add_argument("--disable-popup-blocking")
    #     chrome_options.add_argument("--start-maximized")
        
    #     # Randomize user agent for each run
    #     ua = UserAgent()
    #     user_agent = ua.random
    #     chrome_options.add_argument(f"user-agent={user_agent}")
        
    #     chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    #     chrome_options.add_experimental_option("useAutomationExtension", False)
    #     #chrome_options.add_argument(f"user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data")
    #     driver_version = ChromeDriverManager().install()
    #     self.driver = webdriver.Chrome(service=Service(driver_version), options=chrome_options)
    #     self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    #     print("WebDriver initialized successfully with user agent:", user_agent)

    def setup_driver(self):
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-popup-blocking")
        chrome_options.add_argument("--start-maximized")
        # Add browser fingerprinting protection
        chrome_options.add_argument("--disable-features=IsolateOrigins,site-per-process")
        # Add more realistic screen dimensions and color depth
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--force-device-scale-factor=1")
        chrome_options.add_argument("--force-color-profile=srgb")    
        # Randomize user agent
        ua = UserAgent()
        user_agent = ua.random
        chrome_options.add_argument(f"user-agent={user_agent}")
        # Disable automation flags
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
        chrome_options.add_experimental_option("useAutomationExtension", False)
        # Consider using a persistent profile instead of cookies
        # chrome_options.add_argument(f"user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\UserData\\GlassdoorProfile")
        driver_version = ChromeDriverManager().install()
        self.driver = webdriver.Chrome(service=Service(driver_version), options=chrome_options)
        # Execute additional JavaScript to hide automation
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self.driver.execute_script("window.navigator.chrome = { runtime: {} };")
        self.driver.execute_script("Object.defineProperty(navigator, 'plugins', { get: () => [1, 2, 3, 4, 5] });")
        self.driver.execute_script("Object.defineProperty(navigator, 'languages', { get: () => ['en-US', 'en', 'es'] });")
        
        print("WebDriver initialized successfully with user agent:", user_agent)

    def human_like_delay(self, min_time=2, max_time=10):
        time.sleep(random.uniform(min_time, max_time))

    def type_text(self, element, text):
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(0.1, 0.5))

    # def click_element(self, element):
    #     # Ensure the element is in the viewport
    #     self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
        
    #     # Adding some random mouse movement inside the element's bounds
    #     element_location = element.location
    #     element_size = element.size
    #     random_x = random.uniform(element_location['x'] - 20, element_location['x'] + element_size['width'] + 20)
    #     random_y = random.uniform(element_location['y'] - 20, element_location['y'] + element_size['height'] + 20)
        
    #     # Move the mouse to a random offset inside the element
    #     ActionChains(self.driver).move_to_element_with_offset(element, random_x - element_location['x'], random_y - element_location['y']).pause(random.uniform(0.5, 1.5)).perform()
    #     ActionChains(self.driver).move_to_element(element).pause(random.uniform(0.5, 1.5)).perform()
    #     element.click()
    #     self.human_like_delay()
    def click_element(self, element):
        try:
            # First ensure the element is visible and scrolled into view
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center', behavior: 'smooth'});", element)
            self.human_like_delay(0.5, 1.5)
            
            # Instead of manual coordinate calculations, use ActionChains properly
            actions = ActionChains(self.driver)
            
            # Move to a random point near the element first
            actions.move_by_offset(
                random.randint(-50, 50), 
                random.randint(-50, 50)
            ).perform()
            self.human_like_delay(0.3, 0.8)
            
            # Then move to the element itself with a realistic cursor path
            actions.move_to_element(element).perform()
            self.human_like_delay(0.3, 0.7)
            
            # Add slight jitter within the element
            element_size = element.size
            x_offset = random.uniform(element_size['width'] * 0.2, element_size['width'] * 0.8) - element_size['width']/2
            y_offset = random.uniform(element_size['height'] * 0.2, element_size['height'] * 0.8) - element_size['height']/2
            actions.move_to_element_with_offset(element, x_offset, y_offset).perform()
            self.human_like_delay(0.2, 0.6)
            
            # Finally click the element
            actions.click().perform()
            self.human_like_delay(0.5, 2)
            return True
        except Exception as e:
            print(f"Failed to click element: {e}")
            return False


    def load_cookies(self):
        try:
            self.driver.get("https://www.glassdoor.co.in/Job/index.htm")
            cookies = pickle.load(open("cookies.pkl", "rb"))
            for cookie in cookies:
                self.driver.add_cookie(cookie)
            self.driver.refresh()
            self.human_like_delay()
            print("Cookies loaded successfully")
        except Exception as e:
            print(f"Failed to load cookies: {str(e)}")

    def search_jobs(self, keywords, location):
        try:
            time.sleep(5)
            self.driver.get("https://www.glassdoor.co.in/Job/index.htm")
            time.sleep(5)
            search_field = self.wait.until(EC.presence_of_element_located((By.ID, "searchBar-jobTitle")))
            search_field.click()
            self.human_like_delay(0.5, 1.5)
            search_field.clear()
            self.human_like_delay(0.5, 1.5)
            self.type_text(search_field, keywords)
            self.human_like_delay(1, 3)
            
            location_field = self.wait.until(EC.presence_of_element_located((By.ID, "searchBar-location")))
            location_field.click()
            self.human_like_delay(0.5, 1.5)
            location_field.clear()
            self.human_like_delay(0.5, 1.5)
            self.type_text(location_field, location)
            self.human_like_delay(2, 4)
            
            # Hit enter or find and click search button
            if random.choice([True, False]):
                location_field.send_keys(Keys.RETURN)
            else:
                search_button = self.driver.find_element(By.XPATH, "//button[contains(@class, 'search') or contains(@type, 'submit')]")
                self.click_element(search_button)
            
            self.human_like_delay(5, 10)
            print("Job search completed successfully")
            return True
        except Exception as e:
            print(f"Search failed: {str(e)}")
            return False

    def apply_to_jobs(self):
        try:
            self.human_like_delay()
            job_listings = self.wait.until(EC.presence_of_all_elements_located((By.CLASS_NAME, "JobsList_jobListItem__wjTHv")))
            print(f"Found {len(job_listings)} job listings")
            self.human_like_delay()
            
            for job in job_listings[:5]:  # Limit to 5 jobs to avoid detection from excessive requests
                try:
                    self.click_element(job)
                    print("Applying to job...")
                    self.human_like_delay()
                    try:
                        easy_apply_button = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, '//button[@data-test="easyApply"][@aria-disabled="false"]'))
                        )
                        self.click_element(easy_apply_button)
                        print("Easy Apply button clicked.")
                        self.human_like_delay()
                        
                        # Switch to new window if it opens
                        WebDriverWait(self.driver, 10).until(lambda d: len(d.window_handles) > 1)
                        self.driver.switch_to.window(self.driver.window_handles[1])
                        
                        if self.select_resume():
                            self.human_like_delay()
                            self.click_continue_button()
                        elif self.is_verification_required():
                            print("Human verification required. Complete it manually.")
                            input("Press Enter after verification...")
                            time.sleep(random.uniform(10, 30))  # Extended delay post-captcha
                        elif self.fill_job_details():
                            self.human_like_delay()
                            self.click_continue_button()
                            self.human_like_delay()
                            self.click_continue_button()
                            self.human_like_delay()
                            continue
                        else:
                            self.driver.close()
                            #self.driver.switch_to.window(self.driver.window_handles[0])
                            print("unable to detect resume,captcha and job details form")
                            continue                     
                        #Close the application window and switch back
                        self.driver.close()
                        self.driver.switch_to.window(self.driver.window_handles[0])
                        continue   
                    except TimeoutException:
                        print("Job does not have Easy Apply.")
                        self.driver.switch_to.window(self.driver.window_handles[0])
                        continue
                
                except Exception as e:
                    print(f"Error applying to job: {e}")
                    self.human_like_delay()
                    if len(self.driver.window_handles) > 1:
                        self.driver.close()
                        self.driver.switch_to.window(self.driver.window_handles[0])
                    continue
        except Exception as e:
            print(f"Error loading job listings: {e}")

    def select_resume(self):
        try:
            resume_radio_button = WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.XPATH, "//label[contains(@for, 'ihl-useId')]//span[contains(text(),'DEEPAK GARG RESUME.pdf')]"))
            )
            self.click_element(resume_radio_button)
            print("Resume selected.")
            return True
        except TimeoutException:
            print("Resume selection failed.")
            return False
        
    # def fill_job_details(self):
    #     try:
    #         self.human_like_delay()
    #         job_title_input = self.driver.find_element(By.XPATH, '//input[@name="jobTitle"]')
    #         company_input = self.driver.find_element(By.XPATH, '//input[@name="company"]')
    #         job_title_input.type_text(job_title_input,"Data Science & ML Developer")
    #         self.human_like_delay()
    #         company_input.type_text(company_input,"Appsquadz")
    #         self.human_like_delay()
    #         return True
    #     except Exception:
    #         print("Error: Failed to fill job details at the time of filling the details.")
    #         #self.driver.close()
    #         #self.driver.switch_to.window(self.driver.window_handles[0])
    #         return False
    def fill_job_details(self):
        try:
            self.human_like_delay()
            # First check if these elements exist before trying to interact
            try:
                job_title_input = WebDriverWait(self.driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, '//input[@name="jobTitle"]'))
                )
                company_input = WebDriverWait(self.driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, '//input[@name="company"]'))
                )  
                # Clear fields first (with random delays)
                job_title_input.click()
                self.human_like_delay(0.5, 1.5)
                job_title_input.clear()
                self.human_like_delay(0.5, 1.5)
                # Type text correctly
                self.type_text(job_title_input, "Data Science & ML Developer")
                self.human_like_delay(1, 3)
                company_input.click()
                self.human_like_delay(0.5, 1.5)
                company_input.clear()
                self.human_like_delay(0.5, 1.5)
                self.type_text(company_input, "Appsquadz")
                self.human_like_delay(1, 2)
                return True
            except Exception as e:
                print(f"Error finding form fields: {e}")
                return False
        except Exception as e:
            print(f"Error: Failed to fill job details: {e}")
            return False

    def click_continue_button(self):
        try:
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            self.human_like_delay()
            continue_button = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//button[@data-testid='continue-button']"))
            )
            self.click_element(continue_button)
            print("Continue button clicked.")
        except TimeoutException:
            print("Continue button not found.")

        try:
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            self.human_like_delay()
            submit_button = self.driver.find_element(By.CSS_SELECTOR, "button.ia-continueButton")
            self.click_element(submit_button)
            print("Submit button clicked.")
            return True
        except TimeoutException:
            print("Submit button not found.")

    def is_verification_required(self):
        try:
            WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.XPATH, "//h2[contains(text(), 'Verify') or contains(text(), 'CAPTCHA')]"))
            )
            return True
        except TimeoutException:
            return False

    def save_results(self):
        df = pd.DataFrame(self.applications)
        df.to_excel("glassdoor_applications.xlsx", index=False)
        print("Results saved.")

    def run(self, keywords, location):
        self.load_cookies()
        if self.search_jobs(keywords, location):
            self.apply_to_jobs()
        self.save_results()
        self.driver.quit()

if __name__ == "__main__":
    bot = GlassdoorJobAutomation()
    bot.run("Data Analyst", "New Delhi")