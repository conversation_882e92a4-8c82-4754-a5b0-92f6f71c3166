import time
import logging
import pandas as pd
from typing import List, Dict
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver import Keys
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import (
    TimeoutException,
    NoSuchElementException,
    StaleElementReferenceException,
    ElementClickInterceptedException
)
from selenium.webdriver.support.ui import WebDriverWait


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('glassdoor_automation.log'),
        logging.StreamHandler()
    ]
)

class GlassdoorAutomation:
    def __init__(self, config: Dict):
        self.config = config
        self.driver = None
        self.wait = None
        self.applications: List[Dict] = []
        self.retry_count = 3
        self.locators = {
            'search_title': (By.ID, 'searchBar-jobTitle'),
            'search_location': (By.ID, 'searchBar-location'),
            'job_listings': (By.CSS_SELECTOR, '.JobsList_jobListItem__JBBUV'),
            'apply_button': (By.XPATH, '//button[contains(., "Apply Now")]'),
            'submit_button': (By.XPATH, '//button[contains(., "Submit Application")]'),
            'resume_input': (By.CSS_SELECTOR, 'input[type="file"]'),
            # Add all other locators here
        }
        
    def initialize_driver(self):
        """Initialize Chrome WebDriver with advanced configuration"""
        chrome_options = webdriver.ChromeOptions()
        
        # Configuration management
        chrome_options.binary_location = self.config['chrome']['binary_location']
        chrome_options.add_argument(f"user-data-dir={self.config['chrome']['user_data_dir']}")
        chrome_options.add_argument(f"profile-directory={self.config['chrome']['profile_dir']}")
        
        # Performance and stability arguments
        stability_args = [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-b blink-features=AutomationControlled',
            '--disable-gpu',
            '--disable-software-rasterizer',
            '--window-size=1920,1080'
        ]
        for arg in stability_args:
            chrome_options.add_argument(arg)

        # Error-resistant driver initialization
        for attempt in range(self.retry_count):
            try:
                self.driver = webdriver.Chrome(
                    service=Service(ChromeDriverManager().install()),
                    options=chrome_options
                )
                self.wait = WebDriverWait(self.driver, 15, poll_frequency=2)
                self.driver.maximize_window()
                return True
            except (TimeoutException, NoSuchElementException) as e:
                logging.error(f"Driver initialization attempt {attempt+1} failed: {str(e)}. URL: {self.config.get('target_url', 'N/A')}")

                if attempt == self.retry_count - 1:
                    raise RuntimeError("Failed to initialize WebDriver after multiple attempts")
                time.sleep(5)

    def navigate_to_url(self, url: str):
        """Robust URL navigation with load verification"""
        logging.info(f"Navigating to {url}")
        try:
            self.driver.get(url)
            WebDriverWait(self.driver, 20).until(
                lambda d: d.execute_script('return document.readyState') == 'complete'
            )
        except TimeoutException:
            logging.error("Page load timed out")
            raise

    def safe_click(self, locator: tuple, timeout: int = 15):
        """Click element with multiple safety checks"""
        for attempt in range(self.retry_count):
            try:
                element = self.wait.until(EC.element_to_be_clickable(locator))
                element.click()
                return True
            except (StaleElementReferenceException, ElementClickInterceptedException) as e:
                logging.warning(f"Click attempt {attempt+1} failed: {str(e)}")
                time.sleep(2)
        logging.error(f"Failed to click element after {self.retry_count} attempts")
        return False

    def fill_field(self, locator: tuple, value: str, clear: bool = True):
        """Robust text input with validation"""
        try:
            field = self.wait.until(EC.visibility_of_element_located(locator))
            if clear:
                field.send_keys(Keys.CONTROL + 'a')
                field.send_keys(Keys.DELETE)
            field.send_keys(value)
            # Verify input
            if field.get_attribute('value') != value:
                raise ValueError("Field input verification failed")
            return True
        except Exception as e:
            logging.error(f"Failed to fill field: {str(e)}")
            self.capture_screenshot('field_input_error')
            return False

    def handle_application_process(self):
        """Main application workflow with error handling"""
        try:
            if not self.search_jobs():
                return False

            job_listings = self.wait.until(
                EC.presence_of_all_elements_located(self.locators['job_listings'])
            )
            logging.info(f"Found {len(job_listings)} job listings")

            for index, job in enumerate(job_listings):
                try:
                    job.click()
                    self.driver.switch_to.window(self.driver.window_handles[1])
                    
                    application_result = self.process_single_application()
                    self.record_application_result(application_result)
                    
                    self.driver.close()
                    self.driver.switch_to.window(self.driver.window_handles[0])
                except (StaleElementReferenceException, TimeoutException) as e:
                    logging.error(f"Failed to process job {index+1}: {str(e)}. Job title: {job.text if job else 'N/A'}")
                    self.capture_screenshot(f'job_{index+1}_error')

            return True
        except Exception as e:
            logging.error(f"Application process failed: {str(e)}")
            return False

    def process_single_application(self):
        """Handle individual application with recovery mechanisms"""
        try:
            if not self.safe_click(self.locators['apply_button']):
                return 'apply_button_failed'

            if not self.fill_personal_info():
                return 'personal_info_failed'

            if not self.fill_education():
                return 'education_failed'

            if not self.upload_resume():
                return 'resume_upload_failed'

            if not self.submit_application():
                return 'submission_failed'

            return 'success'
        except Exception as e:
            logging.error(f"Application failed: {str(e)}")
            return 'process_error'

    def fill_personal_info(self):
        """Comprehensive personal information form filling"""
        personal_info = self.config['personal_info']
        try:
            self.fill_field((By.ID, 'firstName'), personal_info['first_name'])
            self.fill_field((By.ID, 'lastName'), personal_info['last_name'])
            self.fill_field((By.ID, 'email'), personal_info['email'])
            self.fill_field((By.ID, 'phoneNumber'), personal_info['phone'])
            
            # Handle optional fields
            if personal_info.get('headline'):
                self.fill_field((By.ID, 'headline'), personal_info['headline'])
            
            return True
        except Exception as e:
            logging.error(f"Personal info error: {str(e)}")
            self.capture_screenshot('personal_info_error')
            return False

    def upload_resume(self):
        """Robust resume upload with verification"""
        try:
            resume_input = self.wait.until(
                EC.presence_of_element_located(self.locators['resume_input'])
            )
            resume_input.send_keys(self.config['resume_path'])
            
            # Verify upload
            WebDriverWait(self.driver, 10).until(
                lambda d: 'uploaded' in resume_input.get_attribute('class').lower()
            )
            return True
        except Exception as e:
            logging.error(f"Resume upload failed: {str(e)}")
            self.capture_screenshot('resume_upload_error')
            return False

    def capture_screenshot(self, name: str):
        """Capture screenshot for debugging"""
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        filename = f"screenshot_{name}_{timestamp}.png"
        self.driver.save_screenshot(filename)
        logging.info(f"Screenshot saved as {filename}")

    def execute_retry(self, func, *args, **kwargs):
        """Generic retry decorator for critical operations"""
        for attempt in range(self.retry_count):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logging.warning(f"Attempt {attempt+1} failed for {func.__name__}: {str(e)}")
            if attempt == self.retry_count - 1:
                raise
            time.sleep(2 ** attempt)

    def save_results(self):
        """Save results with multiple format support"""
        try:
            df = pd.DataFrame(self.applications)
            
            # Save to Excel
            df.to_excel(self.config['output_file'], index=False)
            
            # Additional backup in CSV
            df.to_csv(self.config['output_file'].replace('.xlsx', '.csv'), index=False)
            
            logging.info(f"Results saved to {self.config['output_file']}")
            return True
        except (IOError, pd.errors.EmptyDataError) as e:
            logging.error(f"Failed to save results to {self.config.get('output_file', 'N/A')}: {str(e)}")
            return False

    def run(self):
        """Main execution flow with comprehensive error handling"""
        try:
            if not self.initialize_driver():
                raise RuntimeError("Driver initialization failed")

            self.navigate_to_url(self.config['target_url'])
            
            if not self.execute_retry(self.handle_application_process):
                raise RuntimeError("Application process failed")

            if not self.save_results():
                raise RuntimeError("Results saving failed")

            return True
        except Exception as e:
            logging.error(f"Critical error in main execution: {str(e)}")
            self.capture_screenshot('fatal_error')
            return False
        finally:
            if self.driver:
                self.driver.quit()
                logging.info("Browser session closed")

# Configuration Example
config = {
        'personal_info': {
            'first_name': "Deepak",
            'last_name': "Garg",
            'email': "<EMAIL>",
            'phone': "+91 7838630502",
            'address': "Faridabad, Haryana",
            'headline': "Aspiring Data Scientist",
            'cover_letter': "Aspiring Data Scientist with experience in Data Analysis...",
            'resume_path': "C:/Users/<USER>/CPGRAMS/Naukri/DEEPAK GARG RESUME.pdf"
        },
        'education': {
            'school': "Bennett University",
            'degree': "MCA",
            'major': "Data Science",
            'dates': {
                'start': {"month": "August", "day": "1", "year": "2023"},
                'end': {"month": "May", "day": "31", "year": "2025"}
            }
        },
        'experience': [
            {
                'title': "Data Science & ML Developer",
                'company': "Appsquadz",
                'industry': "Data Science",
                'dates': {
                    'start': {"month": "January", "year": "2025"},
                    'end': {"month": "Present", "year": ""}
                }
            }
        ]
    }  # You can set the config with additional personal info or other settings

if __name__ == "__main__":
    automation = GlassdoorAutomation(config)
    if automation.run():
        logging.info("Automation completed successfully")
    else:
        logging.error("Automation failed with errors")
